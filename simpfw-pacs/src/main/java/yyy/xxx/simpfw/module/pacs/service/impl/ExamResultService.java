package yyy.xxx.simpfw.module.pacs.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.IdGenerator;
import yyy.xxx.simpfw.common.annotation.DataSource;
import yyy.xxx.simpfw.common.core.domain.HttpResult;
import yyy.xxx.simpfw.common.core.service.HttpClientService;
import yyy.xxx.simpfw.common.enums.DataSourceType;
import yyy.xxx.simpfw.common.utils.StringUtils;
import yyy.xxx.simpfw.module.pacs.component.JsonConfigService;
import yyy.xxx.simpfw.module.pacs.constants.TableMetadata;
import yyy.xxx.simpfw.module.pacs.dto.RefEntityExample;
import yyy.xxx.simpfw.module.pacs.entity.ExamResultEntityWithBLOBs;
import yyy.xxx.simpfw.module.pacs.entity.RefEntity;
import yyy.xxx.simpfw.module.pacs.mapper.ExamResultEntityMapper;
import yyy.xxx.simpfw.module.pacs.service.ExamInfoService;
import yyy.xxx.simpfw.module.pacs.vo.ExamResultVo;
import yyy.xxx.simpfw.system.service.ISysConfigService;

import java.net.URL;
import java.sql.Ref;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@DataSource(value = DataSourceType.SLAVE)
public class ExamResultService {

    private final ExamResultEntityMapper examResultEntityMapper;

    private final HttpClientService httpClientService;

    private final ISysConfigService configService;

    private final JsonConfigService jsonConfigService;

    private final RefService refService;

    private final ExamInfoService examInfoService;


    public ExamResultService(ExamResultEntityMapper examResultEntityMapper,
                             HttpClientService httpClientService,
                             ISysConfigService configService,
                             JsonConfigService jsonConfigService,
                             RefService refService,
                             ExamInfoService examInfoService) {
        this.examResultEntityMapper = examResultEntityMapper;
        this.httpClientService = httpClientService;
        this.configService = configService;
        this.jsonConfigService = jsonConfigService;
        this.refService = refService;
        this.examInfoService = examInfoService;
    }

    public ExamResultVo computeResultData(ExamResultVo examResultVo) throws Exception {
        String reportConfigJS = getSysConfig("reportDesignTemplateConfig");
        JSONObject reportConfig = JSON.parseObject(reportConfigJS);
        String baseUrl = reportConfig.getString("baseUrl");
        URL urlParser = new URL(baseUrl);
        baseUrl = urlParser.getProtocol() + "://" + urlParser.getHost() + (urlParser.getPort() != -1 ? ":" + urlParser.getPort() : "");
        String reportResultUrl = baseUrl + reportConfig.getString("reportResultApi");

        String reportResultTemplate = jsonConfigService.getReportResultTemplate(examResultVo.getExamItemCode());
        if (StringUtils.isBlank(reportResultTemplate)) {
            throw new RuntimeException("检查项目: " + examResultVo.getExamItemCode() + " 下的reportResultTemplate为空");
        }
        examResultVo.setTemplatePath(reportResultTemplate);

        log.info("开始请求：{}", reportResultUrl);
        Map<String, Object> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        HttpResult httpResult = httpClientService.doPost(reportResultUrl, JSON.toJSONString(examResultVo), headers, null);
        log.info("报表计算结构化数据返回：{}", httpResult);
        ExamResultVo computeResult = JSON.parseObject(httpResult.getBody(), ExamResultVo.class);
        examResultVo.setExamConclusion(computeResult.getExamConclusion());
        examResultVo.setExamDiagnosis(computeResult.getExamDiagnosis());
        examResultVo.setExamSuggestion(computeResult.getExamSuggestion());
        return examResultVo;
    }

    public void insert(ExamResultVo examResultVo) {
        ExamResultEntityWithBLOBs entity = examResultVo.toEntity();
        String reportId = examInfoService.makeExamNo(null, "examResult");
        entity.setReportId(reportId);
        examResultEntityMapper.insertSelective(entity);
        examResultVo.setId(String.valueOf(entity.getId()));
        examResultVo.setReportId(reportId);
    }

    public void insertRef(String examSerialNo,
                          String reportId) {
        Long examSerilNoLong = Long.valueOf(examSerialNo);
        Long reportIdLong = Long.valueOf(reportId);
        RefEntityExample example = new RefEntityExample();
        example.createCriteria().andObjectId1EqualTo(examSerilNoLong);
        List<RefEntity> currentRefs = refService.getRefList(TableMetadata.R_EXAM_INFO_EXAM_RESULT, example);
        if (CollectionUtils.isNotEmpty(currentRefs)) {
            log.info("检查 {} 已存在examResult引用 {}, 不进行自动匹配", examSerialNo, currentRefs);
            return;
        }
        RefEntity refEntity = new RefEntity();
        refEntity.setObjectId1(examSerilNoLong);
        refEntity.setObjectId2(reportIdLong);
        refService.fullUpdateByObjectId1(Collections.singletonList(refEntity));
    }

    @DataSource(value = DataSourceType.MASTER)
    private String getSysConfig(String key) {
        return configService.selectConfigByKey(key);
    }
}
