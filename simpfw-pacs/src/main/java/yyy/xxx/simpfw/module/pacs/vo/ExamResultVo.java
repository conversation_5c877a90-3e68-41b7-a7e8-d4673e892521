package yyy.xxx.simpfw.module.pacs.vo;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import yyy.xxx.simpfw.module.pacs.entity.ExamResultEntityWithBLOBs;

import java.util.Date;

@Data
public class ExamResultVo {

    private String id;

    private String reportId;

    private String examItemCode;

    private String deviceDatasourceCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;

    private String patientName;

    private String patientGenderCode;

    private String patientExamAge;

    private String registNumber;

    private String medicalRecordNumber;

    private String medicalCardNumber;

    private String medicalVisitNumber; //就诊号（门诊号，住院流水号）

    private String examConclusion;

    private String examDiagnosis;

    private String examSuggestion;

    private JSONObject examResult;

    private String templatePath;

    public ExamResultEntityWithBLOBs toEntity() {
        ExamResultEntityWithBLOBs entity = new ExamResultEntityWithBLOBs();
        entity.setExamItemCode(examItemCode);
        entity.setDeviceDatasourceCode(deviceDatasourceCode);
        entity.setUploadTime(uploadTime);
        entity.setPatientName(patientName);
        entity.setPatientGenderCode(patientGenderCode);
        entity.setPatientExamAge(patientExamAge);
        entity.setRegistNumber(registNumber);
        entity.setMedicalRecordNumber(medicalRecordNumber);
        entity.setMedicalCardNumber(medicalCardNumber);
        entity.setMedicalVisitNumber(medicalVisitNumber);
        entity.setExamConclusion(examConclusion);
        entity.setExamDiagnosis(examDiagnosis);
        entity.setExamSuggestion(examSuggestion);
        return entity;
    }
}
