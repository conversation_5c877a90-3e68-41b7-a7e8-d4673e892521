package yyy.xxx.simpfw.module.pacs.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import yyy.xxx.common.net.storage.StorageInterface;
import yyy.xxx.common.net.storage.StorageInterfaceFactory;
import yyy.xxx.simpfw.common.annotation.Log;
import yyy.xxx.simpfw.common.core.controller.BaseController;
import yyy.xxx.simpfw.common.core.domain.AjaxResult;
import yyy.xxx.simpfw.common.core.domain.entity.SysConfig;
import yyy.xxx.simpfw.common.core.domain.entity.SysDept;
import yyy.xxx.simpfw.common.core.domain.entity.SysDictData;
import yyy.xxx.simpfw.common.core.domain.entity.SysUser;
import yyy.xxx.simpfw.common.core.domain.model.LoginUser;
import yyy.xxx.simpfw.common.core.page.TableDataInfo;
import yyy.xxx.simpfw.common.core.redis.RedisCache;
import yyy.xxx.simpfw.common.enums.BusinessType;
import yyy.xxx.simpfw.common.utils.CryptoUtil;
import yyy.xxx.simpfw.common.utils.DateUtils;
import yyy.xxx.simpfw.common.utils.SecurityUtils;
import yyy.xxx.simpfw.common.utils.ServletUtils;
import yyy.xxx.simpfw.common.utils.file.FileUtils;
import yyy.xxx.simpfw.common.utils.file.ImageUtils;
import yyy.xxx.simpfw.common.utils.http.WebUtil;
import yyy.xxx.simpfw.common.utils.ip.IpUtils;
import yyy.xxx.simpfw.framework.auth.QRAuthConst;
import yyy.xxx.simpfw.framework.auth.bo.CertAuthResponse;
import yyy.xxx.simpfw.framework.auth.bo.QRAuthStatus;
import yyy.xxx.simpfw.framework.auth.bo.gxca.GetDoctorInfoResponse;
import yyy.xxx.simpfw.framework.auth.service.*;
import yyy.xxx.simpfw.framework.config.OCRConfig;
import yyy.xxx.simpfw.module.pacs.Const;
import yyy.xxx.simpfw.module.pacs.bo.SignInfo;
import yyy.xxx.simpfw.module.pacs.bo.StorageParam;
import yyy.xxx.simpfw.module.pacs.component.JsonConfigService;
import yyy.xxx.simpfw.module.pacs.constants.*;
import yyy.xxx.simpfw.module.pacs.dict.ExamEnum;
import yyy.xxx.simpfw.module.pacs.dict.ResultStatus;
import yyy.xxx.simpfw.module.pacs.dto.ExamFileEntityExample;
import yyy.xxx.simpfw.module.pacs.dto.FileInfoDto;
import yyy.xxx.simpfw.module.pacs.dto.OCRMatchResult;
import yyy.xxx.simpfw.module.pacs.entity.*;
import yyy.xxx.simpfw.module.pacs.mapper.ExamInfoMapper;
import yyy.xxx.simpfw.module.pacs.ocr.*;
import yyy.xxx.simpfw.module.pacs.service.*;
import yyy.xxx.simpfw.module.pacs.service.impl.ExamCommonLogService;
import yyy.xxx.simpfw.module.pacs.service.impl.ExamFileService;
import yyy.xxx.simpfw.module.pacs.service.impl.ExamResultService;
import yyy.xxx.simpfw.module.pacs.utils.FileUrlDesUtil;
import yyy.xxx.simpfw.module.pacs.utils.FileUtil;
import yyy.xxx.simpfw.module.pacs.utils.PdfUtil;
import yyy.xxx.simpfw.module.pacs.vo.DicomInfoVo;
import yyy.xxx.simpfw.module.pacs.vo.ExamInfoVo;
import yyy.xxx.simpfw.module.pacs.vo.ExamResultVo;
import yyy.xxx.simpfw.module.pacs.vo.PatientVo;
import yyy.xxx.simpfw.system.mapper.SysConfigMapper;
import yyy.xxx.simpfw.system.service.ISysConfigService;
import yyy.xxx.simpfw.system.service.ISysDictDataService;
import yyy.xxx.simpfw.system.service.ISysUserService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URLDecoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/exammanagement/examInfo/report")
public class ExamReportController extends BaseController {

    @Autowired
    private OCRService ocrService;

    @Autowired
    private ExamInfoService infoService;

    @Autowired
    private ExamReportService service;

    @Autowired
    private QRAuthService qrAuthSerice;
    @Autowired
    private QRAuthRelService qrAuthRelSerice;
    @Autowired
    private ExamWithdrawService examWithdrawService;

    @Autowired
    private ReportInterService reportInterService;

    @Autowired
    private ExamReportHelpService reportHelperService;

    @Autowired
    private DicomImageTransferService dicomImageTransferService;

    @Autowired
    private CertAuthService certAuthService;

    @Autowired
    private GXCAQRAuthService gxcaqrAuthService;

    @Autowired
    private TestAuthService testAuthService;

    @Autowired
    private SysConfigMapper configMapper;

    @Autowired
    private SignInfoService signInfoService;

    @Autowired
    private DicomImageIndexService dicomImageIndexService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ExamInfoMapper examInfoMapper;

    @Autowired
    private ExamFileService examFileService;
    @Autowired
    private ExamReportService examReportService;

    @Autowired
    private JsonConfigService jsonConfigService;

    @Autowired
    private DictConvertToCCC dictConvertToCCC;

    @Autowired
    private DicomInfoService dicomInfoService;
    @Autowired
    private ExamInfoService examInfoService;
    @Autowired
    private ExamCommonLogService examCommonLogService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private ExamDataCtrlService dataCtrlService;

    @Autowired
    private ExamResultService examResultService;

    @PostMapping(value = "/result/save")
    public AjaxResult saveReportResult(@RequestBody ExamResultVo examResultVo) {
        try {
            if (StringUtils.isBlank(examResultVo.getExamConclusion())
                    || StringUtils.isBlank(examResultVo.getExamDiagnosis())
                    || StringUtils.isBlank(examResultVo.getExamSuggestion())) {
                examResultVo = examResultService.computeResultData(examResultVo);
            }

            examResultService.insert(examResultVo);
            PatientVo patientVo = new PatientVo();
            patientVo.setRegistNo(examResultVo.getRegistNumber());
            patientVo.setHealthCardId(examResultVo.getMedicalCardNumber());
            patientVo.setName(examResultVo.getPatientName());
            patientVo.setGender(examResultVo.getPatientGenderCode());

            ExamInfoVo examInfoVo = new ExamInfoVo();
            if (StringUtils.isNotBlank(examResultVo.getMedicalRecordNumber())) {
                examInfoVo.setInpNo(examResultVo.getMedicalRecordNumber());
            } else {
                examInfoVo.setInpNo(examResultVo.getMedicalVisitNumber());
            }
            examInfoVo.setExamAge(examResultVo.getPatientExamAge());
            logger.info("开始匹配 examInfo: {} patientInfo: {}", examInfoVo, patientVo);
            List<ExamInfoVo> matchResult = getPatient(examInfoVo, patientVo, Collections.singletonList(examResultVo.getExamItemCode()));
            if (CollectionUtils.isEmpty(matchResult)) {
                logger.info("没有匹配到任何检查");
            } else if (matchResult.size() > 1) {
                logger.info("匹配到多条检查：{}", matchResult.stream().map(ExamInfoVo::getId).collect(Collectors.toList()));
            } else {
                examResultService.insertRef(matchResult.get(0).getExamSerialNo(), examResultVo.getReportId());
            }

            return AjaxResult.success();
        } catch (Exception e) {
            logger.error("保存检查结果(examResult)失败", e);
            return AjaxResult.error(e.getMessage());
        }
    }


    /**
     * 保存报告
     */
    @Log(title = "报告书写", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/save")
    @PreAuthorize("@ss.hasAnyPermi('exam-report:write')")
    public AjaxResult save(@RequestBody ExamInfo entity) {

        ExamInfo info = infoService.selectOne(entity);
        if (null == info) {
            return AjaxResult.error("检查不存在。");
        }
        //状态检查，已登记、已检查和书写完成可保存报告
        SysDictData resultStatus = info.getResultStatus();
        String resultStatusCode;
        if (null == resultStatus || !(
                ResultStatus.REGIST.is((resultStatusCode = resultStatus.getDictValue()))
                        || ResultStatus.EXAM.is(resultStatus.getDictValue())
                        || ResultStatus.REPORT.is(resultStatusCode)
        )) {
            String resultStatusLabel = null != resultStatus ? resultStatus.getDictLabel() : "";
            return AjaxResult.error(String.format("无法书写报告，当前检查进度为：%s。", resultStatusLabel));
        }
        //
        infoService.theDoctors(entity);

        return toAjax(service.save(entity));
    }

    /**
     * 获取签名需要的信息
     */
    @Log(title = "获取签名", businessType = BusinessType.GRANT)
    @PreAuthorize("@ss.hasPermi('exam-report:audit')" + " or " +
            "@ss.hasPermi('exam-report:second_audit')" + " or " +
            "@ss.hasPermi('exam-report:third_audit')")
    @PutMapping(value = "/getUserSignInfo")
    public AjaxResult getUserSignInfo(@RequestBody ExamInfo param) {
        //
        LoginUser luser = getLoginUser();
        //qr
        SysUser signUser;
        try {
            Map ret = new HashMap();
            signUser = getSignUser(luser);
            ret.put("signImage", signUser.getAvatar());
            ret.put("signDate", DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD));
            String qrCodeImage = dictConvertToCCC.getQRImage(param);
            ret.put("QRImage", qrCodeImage);
            return AjaxResult.success(ret);
        } catch (IllegalStateException err) {
            return AjaxResult.success("无法获取签名图片。").put(AjaxResult.ERRC_TAG, "qrnoauth");
        }
    }

    /**
     * 获取签名需要的信息
     */
    @Log(title = "打印报告", businessType = BusinessType.GRANT)
    @PreAuthorize("@ss.hasPermi('exam-report:print')")
    @PutMapping(value = "/printReport")
    public AjaxResult printReport(@RequestBody ExamInfo param) {
        //
        return AjaxResult.success();
    }


    @PostMapping("/saveAuditFile")
    @PreAuthorize("@ss.hasPermi('exam-report:audit')" + " or " +
            "@ss.hasPermi('exam-report:second_audit')" + " or " +
            "@ss.hasPermi('exam-report:third_audit')")
    public AjaxResult saveAuditFile(@RequestPart("file") MultipartFile file,
                                    @RequestPart("data") String data) {
        String auditFilePath;
        ExamInfo examInfo;
        try {
            examInfo = JSON.parseObject(data, ExamInfo.class);
            auditFilePath = service.saveAuditFile(examInfo, file);
        } catch (Exception exception) {
            logger.error("审核文件保存失败", exception);
            return AjaxResult.error("审核文件保存失败", exception);
        }

        return AjaxResult.success("审核文件保存成功", auditFilePath);
    }

    /**
     * 审核报告
     * 前端贴签名
     */
    @Log(title = "报告审核", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('exam-report:audit')" + " or " +
            "@ss.hasPermi('exam-report:second_audit')" + " or " +
            "@ss.hasPermi('exam-report:third_audit')")
    @PostMapping(value = "/doAudit")
    public AjaxResult doAudit(@RequestPart("data") String data) {
        ExamInfo param = JSON.parseObject(data, ExamInfo.class);
        ExamInfo examInfo = null;
        // TODO 在这里进行操作 保存pdf，保存3c，更新状态
        try {
            LoginUser luser = getLoginUser();
            Set<String> permissions = luser.getPermissions();
            String resultStatus = param.getResultStatus().getDictValue();
            String fasCode = jsonConfigService.getFinalAuditStatus(param);
            if (StringUtils.equals(fasCode, resultStatus)) {
                String auditStr = "初审";
                if (ResultStatus.SECOND_AUDIT.is(resultStatus)) {
                    auditStr = "二审";
                } else if (ResultStatus.THIRD_AUDIT.is(resultStatus)) {
                    auditStr = "三审";
                }
                return AjaxResult.error("当前检查项目只支持" + auditStr);
            }
            if (!permissions.contains(SecurityUtils.ADMIN_PERMISSION)) {
                if (ResultStatus.AUDIT.is(resultStatus)) {
                    if (!permissions.contains(SecurityUtils.SECOND_AUDIT_PERMISSION)) {
                        return AjaxResult.error("当前用户没有二审的权限");
                    }
                } else if (ResultStatus.SECOND_AUDIT.is(resultStatus)) {
                    if (!permissions.contains(SecurityUtils.THIRD_AUDIT_PERMISSION)) {
                        return AjaxResult.error("当前用户没有三审的权限");
                    }
                } else if (ResultStatus.THIRD_AUDIT.is(resultStatus)) {
                    return AjaxResult.error("目前审核次数最高只支持三审");
                } else if (!permissions.contains(SecurityUtils.AUDIT_PERMISSION)) {
                    return AjaxResult.error("当前用户没有初审的权限");
                }
            }
            //qr
            SysUser signUser;
            try {
                signUser = getSignUser(luser);
            } catch (IllegalStateException err) {
                return AjaxResult.success("无法获取签名图片。").put(AjaxResult.ERRC_TAG, "qrnoauth");
            }
            //获取签字图片
            String signImg;
            try {
                signImg = null != signUser ? signUser.getAvatar() : null;
            } catch (IllegalStateException err) {
                return AjaxResult.success("无法获取签名图片。").put(AjaxResult.ERRC_TAG, "qrnoauth");
            }
            service.doAuditTx(param, signImg);
            //获取最新报告信息，确定是否将报告发送接口
            examInfo = infoService.selectOne(param);
            if (StringUtils.equals(param.getResultStatus().getDictValue(), fasCode)) {
                reportInterService.sendReportCCC(examInfo, ResultStatus.REGIST.getValue());
            }
        } catch (Exception err) {
            //失败不再手动回滚，而是依靠spring事务管理
            /*param.getResultStatus().setDictValue(ResultStatus.REPORT.getValue());
            infoService.updateResultStatus(param);
            examAttachmentService.deleteByExam(param.getId(), ExamReportService.attTypeSign); //清空签名图片*/
            logger.error("审核报告失败", err);
            return AjaxResult.error("审核报告失败");
        }
        return AjaxResult.success(examInfo);
    }


    /**
     * 审核报告
     */
    @Log(title = "报告审核", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('exam-report:audit')")
    @PutMapping(value = "/audit")
    public AjaxResult audit(@RequestBody ExamInfo param) {
        //读取检查信息
        ExamInfo info = infoService.selectOne(param);
        if (null == info) {
            return AjaxResult.error("报告不存在。");
        }
        //
        LoginUser luser = getLoginUser();
        //qr
        SysUser signUser;
        try {
            signUser = getSignUser(luser);
        } catch (IllegalStateException err) {
            return AjaxResult.success("无法获取签名图片。").put(AjaxResult.ERRC_TAG, "qrnoauth");
        }
        //状态检查，书写完成->初审，审核->复审
        SysDictData resultStatus = info.getResultStatus();
        String resultStatusCode;
        if (null != resultStatus && StringUtils.isNotBlank((resultStatusCode = resultStatus.getDictValue()))) {
            //
            if (ResultStatus.REGIST.is(resultStatusCode) || ResultStatus.EXAM.is(resultStatusCode)) {
                param.setExamTime(DateUtils.getNowDate());
                param.setExamDoctor(signUser);
                param.setAuditDoctor(signUser);
            } else if (ResultStatus.REPORT.is(resultStatusCode)) {
                param.setAuditDoctor(signUser);
            } else if (ResultStatus.AUDIT.is(resultStatusCode)) {
                param.setReauditDoctor(signUser);
            } else {
                param.setAuditDoctor(signUser);
            }
        } else {
            return AjaxResult.error("不满足审核条件。");
        }
        //获取签字图片
        String signImg;
        try {
            signImg = null != signUser ? signUser.getAvatar() : null;
        } catch (IllegalStateException err) {
            return AjaxResult.success("无法获取签名图片。").put(AjaxResult.ERRC_TAG, "qrnoauth");
        }
        //当前数据库的工作状态
        param.setResultStatus(resultStatus);
        //
        int resC = service.audit(param);
        //审核同时签字
        if (resC > 0 && (ResultStatus.REPORT.is(resultStatusCode) || ResultStatus.EXAM.is(resultStatusCode) || ResultStatus.REGIST.is(resultStatusCode))) {
            //QRAuthConst.ERRM_NOAUTH
            //return sign(param);
            try {
                if (StringUtils.isNotEmpty(signImg)) {
                    SignInfo signInfo = new SignInfo();
                    signInfo.setUuid(signUser.getPassword());
                    param.setSignInfo(signInfo);
                }
                service.sign(param, signImg);
            } catch (Exception err) {
                //退回书写完成工作状态
                try {
                    resultStatus.setDictValue(ResultStatus.REPORT.getValue());
                    infoService.updateResultStatus(info);
                } catch (Exception er) {
                    logger.error(err.getMessage(), err);
                }
                return AjaxResult.error(err.getMessage());
            }
        }
        //生成报告后发送
        /*if(StringUtils.isNotBlank(info.getOrdId())) {
            reportInterService.sendReport(infoService.selectOne(param));
        }*/
        //
        return toAjax(resC);
    }

    private int batchAuditPdfW(List<Long> param, SysUser signUser) {
        int num = 0;
        for (int i = 0; i < param.size(); i++) {
            ExamInfo info = new ExamInfo();
            info.setId(param.get(i));
            ExamInfo examInfo = infoService.selectOne(info);
            if (null == info) {
                continue;
            }

            examInfo.setSignImage(signUser.getAvatar());

            SysDictData rstatus = examInfo.getResultStatus();

            if (null == rstatus || StringUtils.isBlank(rstatus.getDictValue()) || !ResultStatus.REPORT.is(rstatus.getDictValue())) {
                continue;
            }

            try {
                service.auditPdf(examInfo, signUser);

                service.autoAudit(examInfo, signUser);
//                examInfo = infoService.selectOne(info);
//                if(null == info) {
//                    throw new IllegalAccessException("没有该检查");
//                }
//                //info.setFileName(fileName);
//                try {
//                    examInfo.getResultStatus().setDictValue(ResultStatus.AUDIT.getValue());
//                    infoService.updateResultStatus(examInfo);
//                } catch (Exception er) { logger.error(er.getMessage(), er); }


                examInfo = infoService.selectOne(info);
                if (null == info) {
                    throw new IllegalAccessException("没有该检查");
                }
                //发送报告到3C
                reportInterService.sendReportCCC(examInfo, ResultStatus.REGIST.getValue());

                //infoService.sendReportStatus(examInfo.getId(), ExamEnum.StatusOfSendReportSucess);

                num++;
            } catch (Exception err) {
                logger.error(err.getMessage(), err);
                //退回书写完成工作状态
                try {
                    examInfo.getResultStatus().setDictValue(ResultStatus.REPORT.getValue());
                    infoService.updateResultStatus(examInfo);
                } catch (Exception er) {
                    logger.error(er.getMessage(), er);
                }
            }
        }

        return num;
    }

    /**
     * pdf报告批量审核
     */
    @Log(title = "pdf报告批量审核", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('exam-report:audit')")
    @RequestMapping(value = "/batchAuditPdf")
    public AjaxResult batchAuditPdf(@RequestParam("examInfos") List<Long> param) {
        //qr
        SysUser signUser;
        try {
            signUser = getSignUser(getLoginUser());
        } catch (IllegalStateException err) {
            return AjaxResult.success("无法获取签名图片。").put(AjaxResult.ERRC_TAG, "qrnoauth");
        }

        int num = batchAuditPdfW(param, signUser);

        AjaxResult r = null;
        r = AjaxResult.success(String.format("审核成功%d份报告，审核失败%d份", num, param.size() - num));
        r.put("numAffected", num);
        return r;
    }

    private boolean selectImage(ExamInfo examInfo) {
        if ((null == examInfo.getImages() || 0 == examInfo.getImages().size()) && examInfo.getDicomStudies().size() > 0) {
            //没有选图，且图像数量大于0
            List<DicomImage> images = dicomImageIndexService.images(examInfo.getDicomStudies().get(examInfo.getDicomStudies().size() - 1).getStudyInstanceUid());

            if (1 == images.size()) {
                DicomImage dicomImageNew = images.get(images.size() - 1);
                ExamAttachment examAttachment = new ExamAttachment();

                String regex = "(https?|ftp)://[^:]+(:\\d+)?";
                String replacement = "";
                String path = dicomImageNew.getFileUrl().replaceAll(regex, "");
                examAttachment.setPath(path);
                examAttachment.setFileType("dcm");
                List<ExamAttachment> list = new ArrayList<>();
                list.add(examAttachment);
                examInfo.setImages(list);
                return true;
            }
            return false;
        }
        return true;
    }


    /**
     * 报告批量审核
     */
    @Log(title = "报告批量审核", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('exam-report:audit')")
    @RequestMapping(value = "/batchAudit")
    public AjaxResult batchAudit(@RequestParam("examInfos") Long[] param) {
        //qr
        SysUser signUser;
        try {
            signUser = getSignUser(getLoginUser());
        } catch (IllegalStateException err) {
            return AjaxResult.success("无法获取签名图片。").put(AjaxResult.ERRC_TAG, "qrnoauth");
        }

        int num = 0;
        for (int i = 0; i < param.length; i++) {
            ExamInfo info = new ExamInfo();
            info.setId(param[i]);
            ExamInfo examInfo = infoService.selectOne(info);
            if (null == info) {
                continue;
            }

            SysDictData rstatus = examInfo.getResultStatus();

            if (null == rstatus || StringUtils.isBlank(rstatus.getDictValue()) || !ResultStatus.REPORT.is(rstatus.getDictValue())) {
                continue;
            }

            try {
                if (!selectImage(examInfo)) continue;
                service.save(examInfo);
                service.autoAudit(examInfo, signUser);
                examInfo = infoService.selectOne(info);
                if (null == info) {
                    throw new IllegalAccessException("没有该检查");
                }
                //info.setFileName(fileName);
                uploadReportDoc(examInfo);

                num++;
            } catch (Exception err) {
                logger.error(err.getMessage(), err);
                //退回书写完成工作状态
                try {
                    examInfo.getResultStatus().setDictValue(ResultStatus.REPORT.getValue());
                    infoService.updateResultStatus(examInfo);
                } catch (Exception er) {
                    logger.error(err.getMessage(), err);
                }
            }


        }
        AjaxResult r = null;
        r = AjaxResult.success(String.format("审核成功%d份报告，审核失败%d份", num, param.length - num));
        r.put("numAffected", num);
        return r;
    }

    /**
     * jpg报告批量上传
     */
    @Log(title = "报告批量上传", businessType = BusinessType.UPDATE)
    @RequestMapping("/batchReport")
    public AjaxResult batchReport(@RequestParam("ExamInfos") String ExamInfoArs, @RequestParam("files") MultipartFile[] files) {
        try {

            List<ExamInfo> ExamInfos = JSON.parseArray(ExamInfoArs, ExamInfo.class);

            AjaxResult r = null;
            int fileTotal = 0;
            int successNum = 0;

            //构建文件名对应id map
            Map<String, Long> fileNameMapId = new HashMap<>();
            for (ExamInfo examInfo : ExamInfos) {
                for (String fileName : examInfo.getFileNames()) {
                    fileTotal++;
                    fileNameMapId.put(fileName, examInfo.getId());
                }
            }

//            JSONObject fileExamMapObj = null;//JSON.parseObject(fileExamMap.toString());

            for (int i = 0; i < files.length; i++) {
                String fileName = files[i].getOriginalFilename();
                fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
//                JSONObject examJson = fileExamMapObj.getJSONObject(fileName);
//                if(null==examJson) continue;
//                ExamInfo examInfo =  JSON.toJavaObject(examJson, ExamInfo.class);
                Long examId = fileNameMapId.get(fileName);
                if (null == examId) continue;
                ExamInfo examInfo = new ExamInfo();
                examInfo.setId(examId);

                MultipartFile[] file = new MultipartFile[]{files[i]};
                //examInfo.setSignImage(signUser.getAvatar());
                //int num = service.batchReport(examInfo, file, signUser);

                try {
                    dicomImageTransferService.resback(examInfo.getId(), file, getLoginUser().getUser(), 2, examInfo.getImageNo());

                    successNum++;
                } catch (Exception err) {
                    logger.error(err.getMessage(), err);
                    //退回书写完成工作状态
                    try {
                        examInfo.getResultStatus().setDictValue(ResultStatus.REGIST.getValue());
                        infoService.updateResultStatus(examInfo);
                    } catch (Exception er) {
                        logger.error(err.getMessage(), err);
                    }
                }
            }
            r = AjaxResult.success(String.format("上传成功%d份报告，上传失败%d份", successNum, fileTotal - successNum));
            r.put("numAffected", successNum);
            return r;
        } catch (Exception err) {
            String errM = err.getMessage();
            logger.error(errM, err);
            return AjaxResult.error(errM);
        }
    }

    /**
     * 复核报告
     */
   /* @Log(title = "复核报告", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/reaudit")
    public AjaxResult reaudit(@RequestBody ExamInfo param) {
        //读取检查信息
        ExamInfo info = infoService.selectOne(param);
        if (null == info) {
            return AjaxResult.error("报告不存在。");
        }
        //状态检查，审核->复核
        SysDictData resultStatus = info.getResultStatus();
        if (null == resultStatus || !ResultStatus.AUDIT.is(resultStatus.getDictValue())) {
            String resultStatusLabel = null != resultStatus && StringUtils.isNotBlank(resultStatus.getDictLabel()) ?
                    resultStatus.getDictLabel() : "未知";
            return AjaxResult.error(String.format("不满足审核条件，当前检查进度为：%s。", resultStatusLabel));
        }
        //
        LoginUser luser = getLoginUser();
        param.setReauditDoctor(luser.getUser());
        try {
            return toAjax(service.reaudit(param));
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }*/

    /**
     * 召回已审核的报告
     *
     * @param id
     * @param content
     * @return
     */
    @Log(title = "报告召回", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('exam-report:audit')" + " or " +
            "@ss.hasPermi('exam-report:second_audit')" + " or " +
            "@ss.hasPermi('exam-report:third_audit')")
    @RequestMapping(value = "withdrawAudit")
    public AjaxResult withdrawAudit(Long id, String content) throws Exception {
        if (null == id || StringUtils.isBlank(content)) {
            return AjaxResult.error("参数不足。");
        }
        ExamInfo info = infoService.selectById(id);
        if (null == info) {
            return AjaxResult.error("报告不存在。");
        }
        //检查当前状态
        SysDictData resultStatus = info.getResultStatus();
        String resultStatusCode;
        if (null == resultStatus || !(
                ResultStatus.AUDIT.is((resultStatusCode = resultStatus.getDictValue()))
                        || ResultStatus.SECOND_AUDIT.is((resultStatusCode))
                        || ResultStatus.THIRD_AUDIT.is((resultStatusCode))
                        || ResultStatus.REAUDIT.is(resultStatusCode)
                        || ResultStatus.PRINT.is(resultStatusCode)
        )) {
            return AjaxResult.error(String.format("报告无法召回，当前报告进度为：%s。", resultStatus.getDictLabel()));
        }
        LoginUser loginUser = getLoginUser();
        //
        ExamWithdraw withdraw = new ExamWithdraw();
        withdraw.setExamInfo(info);
        withdraw.setWithdrawDoctor(loginUser.getUser());
        withdraw.setWithdrawReason(content);
        resultStatus.setDictValue(ResultStatus.REPORT.getValue());
        examWithdrawService.insert(withdraw);

//        if(StringUtils.isNotBlank(info.getOrdId())) {
//            reportInterService.cancelAudit(withdraw);
//        }
        //3C退回已取消
        resultStatus.setDictLabel("已报告");
        try {
            reportInterService.sendReportCCC(info, ResultStatus.AUDIT.getValue());
            infoService.sendReportStatus(info.getId(), ExamEnum.StatusOfSendReportDefault);
        } catch (Exception e) {
            infoService.sendReportStatus(info.getId(), info.getStatusOfSendReport());
            return AjaxResult.error("召回失败:" + e.getMessage());
        }


        return AjaxResult.success("成功召回报告");
    }

    /**
     * 签字
     *
     * @param entity
     * @return
     */
    @Log(title = "报告签字", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/sign")
    public AjaxResult sign(@RequestBody ExamInfo entity) {
        ExamInfo info = infoService.selectOne(entity);
        if (null == info) {
            return AjaxResult.error("检查不存在。");
        }
        if (null != info.getSignDoctor() && StringUtils.isNotBlank(info.getSignDoctor().getUserName())) {
            return AjaxResult.error("报告已签字。");
        }
        //状态检查，审核或已复核的可签字
        SysDictData resultStatus = info.getResultStatus();
        String resultStatusCode;
        if (null == resultStatus || (
                !ResultStatus.AUDIT.is((resultStatusCode = resultStatus.getDictValue()))
                        && !ResultStatus.REAUDIT.is(resultStatusCode)
        )) {
            String resultStatusLabel = null != resultStatus ? resultStatus.getDictLabel() : "已登记";
            return AjaxResult.error(String.format("无法书写报告，当前检查进度为：%s。", resultStatusLabel));
        }
        //当前登录用户
        LoginUser luser = getLoginUser();
        //获取签字图片
        SysUser signUser = getSignUser(luser);//luser.getUser();
        entity.setSignDoctor(signUser);
        //获取签字图片
        String signImg = signUser.getAvatar();
        if (StringUtils.isBlank(signImg)) {
            throw new IllegalStateException("无法获取签名图片。");
        }
        //
        try {
            return toAjax(service.sign(entity, signImg));
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }

    /**
     * 获取签字图片
     */
    private SysUser getSignUser(LoginUser luser) {
        final String token = luser.getToken();
        SysUser signImg = null;

        //获取科室配置文件是否要CA认证
        SysConfig scfg = configMapper.checkConfigKeyUnique(Const.DICT_DEPT_CONFIG_KEY);
        String CAauth = null;
        if (null != scfg && yyy.xxx.simpfw.common.utils.StringUtils.isNotBlank(scfg.getConfigValue())) {
            JSONObject cfg = JSON.parseObject(scfg.getConfigValue());
            JSONObject deptCfg = cfg.getJSONObject(luser.getUser().getDept().getDeptCode());
            if (null != deptCfg) {
                CAauth = deptCfg.getString("CAauth");
                if (null != CAauth && CAauth.equals("0")) {
                    return luser.getUser();
                }
            }
        }
        //try {

        //不缓存
        //先从缓存获取
//        final String cacheKey = CacheUtil.cacheKey("signUser");//"signatureImg"
//        Map<String, SysUser> cache = redisCache.getCacheMap(cacheKey);
//        signImg = null != cache? cache.get(token) : null;
//        if(null != signImg) {
//            return signImg;
//        }

        //缓存没有则调用接口
        QRAuthStatus qrAuthStatus = null;
        boolean cqService = false;
        boolean useGXCA = false;
        boolean enableTest = false;
        SignInfo signInfoU = new SignInfo();

        try {
            try {
                enableTest = testAuthService.checkConfig();
                useGXCA = gxcaqrAuthService.checkConfig();
                cqService = certAuthService.checkConfig();
            } catch (Exception err) {
            }

            //测试环境优先级最高
            if (enableTest) {
                qrAuthStatus = testAuthService.getQRAuthStatus();
                Date authstarttime = new Date();
                Date authendtime = new Date();
                signInfoU.setAuthstarttime(authstarttime);
                signInfoU.setAuthendtime(authendtime);
            }
            //易手签有配置参数，优先使用
            else if (useGXCA) {
                GetDoctorInfoResponse doctorInfoResponse = gxcaqrAuthService.getDoctorInfoByCache(token);
                if (null == doctorInfoResponse) {
                    throw new IllegalStateException("CA登录状态已过期，请重新扫码登录认证。");
                }
                qrAuthStatus = new QRAuthStatus();
                if (StringUtils.isBlank(doctorInfoResponse.getSignImg())) {
                    throw new RuntimeException("用户签章图片为空，请联系CA服务商排查原因");
                }
                qrAuthStatus.setSignatureImg(doctorInfoResponse.getSignImg());
                qrAuthStatus.setUserId(doctorInfoResponse.getDoctorJobNo());
                qrAuthStatus.setUserName(doctorInfoResponse.getDoctorName());
                Date authstarttime = new Date();
                Date authendtime = new Date();
                signInfoU.setAuthstarttime(authstarttime);
                signInfoU.setAuthendtime(authendtime);
            }
            //网证通有配置参数，优先使用
            else if (cqService) {
                String picBase64 = certAuthService.downloadsealpic(token);
                CertAuthResponse<Object> resUser = certAuthService.getTokenUser(token);
                Map<String, Object> certUser = null != resUser ? resUser.getContents() : null;
                if (null == certUser) {
                    throw new IllegalStateException("无法获取操作人信息.");
                }

                qrAuthStatus = new QRAuthStatus();
                qrAuthStatus.setSignatureImg(picBase64);
                qrAuthStatus.setUserId(certUser.get("uid").toString());
                qrAuthStatus.setUserName(certUser.get("name").toString());
                Date authstarttime = new Date();
                Date authendtime = new Date();
                signInfoU.setAuthstarttime(authstarttime);
                signInfoU.setAuthendtime(authendtime);
            } else {
                //没有配置网证通参数，使用医信签
                qrAuthStatus = qrAuthSerice.fetchOauthStatus(token);
                Date authstarttime = new Date();
                Date authendtime = new Date();
                signInfoU.setAuthstarttime(authstarttime);
                signInfoU.setAuthendtime(authendtime);
            }

        } catch (IllegalStateException err) {
            //易手签，直接抛出异常
            if (enableTest || useGXCA) {
                throw err;
            }
            //尝试常规账号关联的扫码账号
            if (QRAuthConst.ERRM_NOAUTH.equals(err.getMessage())) {
                if (cqService) {
                    throw new IllegalStateException(QRAuthConst.ERRM_NOAUTH);
                } else {
                    String qrToken = qrAuthRelSerice.getRelation(token);
                    if (logger.isDebugEnabled()) {
                        logger.debug("{}->{}", token, qrToken);
                    }
                    if (StringUtils.isBlank(qrToken)) {
                        throw new IllegalStateException(QRAuthConst.ERRM_NOAUTH);
                    }
                    qrAuthStatus = qrAuthSerice.fetchOauthStatus(qrToken);
                }
            }
        }
        //库中最新签名
        SignInfo signInfo = new SignInfo();
        signInfo.setUserId(luser.getUser().getUserId());
        SignInfo signInfoLast = signInfoService.selectLastSignInfo(signInfo);

        signImg = new SysUser();

        //请求不到签名
        if (null == qrAuthStatus || null == qrAuthStatus.getSignatureImg()) {
            //授权时间还有效
            if (signInfoLast.getAuthendtime().after(new Date())) {
                qrAuthStatus.setSignatureImg(signInfoLast.getPicbase64());
                qrAuthStatus.setUserId(signInfoLast.getUserId().toString());
                qrAuthStatus.setUserName(signInfoLast.getUsername());
            } else {
                return null;
            }
        }

        //检查是否存在、本科室、有审核权限
//        SysUser user0 = userService.selectUserByUserName(qrAuthStatus.getUserId());

        signImg.setAvatar(qrAuthStatus.getSignatureImg());
        signImg.setUserName(qrAuthStatus.getUserId());
        signImg.setNickName(qrAuthStatus.getUserName());
        //不缓存
//        cache = null != cache? cache : new HashMap<>();
//        cache.put(token, signImg);
//        redisCache.setCacheMap(cacheKey, cache);
//        redisCache.expire(cacheKey, 1, TimeUnit.DAYS);

        boolean isSameFile = false;
        if (null != signInfoLast) {
            try {
                MessageDigest md = MessageDigest.getInstance("MD5");
                md.update(qrAuthStatus.getSignatureImg().getBytes());
                byte[] mdbytes1 = md.digest();
                md.reset();

                md.update(signInfoLast.getPicbase64().getBytes());
                byte[] mdbytes2 = md.digest();
                isSameFile = MessageDigest.isEqual(mdbytes1, mdbytes2);
            } catch (NoSuchAlgorithmException e) {
                logger.error(e.getMessage(), e);
            }
        }

        if (isSameFile) {
            signInfoU.setId(signInfoLast.getId());
            signInfoService.updateSignInfo(signInfoU);
            signImg.setPassword(signInfoLast.getUuid());
        } else {
            signInfoU.setUserId(luser.getUserId());
            signInfoU.setUsername(luser.getUsername());
            signInfoU.setPicbase64(qrAuthStatus.getSignatureImg());
            signInfoU.setUuid(yyy.xxx.simpfw.common.utils.StringUtils.uid());

            signInfoService.insertSignInfo(signInfoU);
            signImg.setPassword(signInfoU.getUuid());
        }

        return signImg;
    }


    /**
     * 向3C发报告
     *
     * @param report 报告信息
     * @return
     */
    @Log(title = "报告生成", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/uploadReportDoc")
    public AjaxResult uploadReportDoc(@RequestBody ExamInfo report) throws Exception {
        if (null == report || null == report.getId() || StringUtils.isBlank(report.getExamNo())/*
            || (StringUtils.isBlank(report.getReportUrlPdf()) && StringUtils.isBlank(report.getReportUrlJpg()))*/) {
            logger.error("报告信息不足,报告id {}", report.getOrdIds());
            throw new IllegalAccessException("报告信息不足。");
        }
        //保存报告
        try {
            SysUser signUser = getSignUser(getLoginUser());
            if (null == signUser) {
                logger.error("无法获取签名, 报告id {}", report.getOrdIds());
                throw new IllegalAccessException("无法获取签名。");
            }
            report.setSignImage(signUser.getAvatar());
            //1.保存报告内容
            int numAffected = 1;
            //service.saveDoc(report);
            //service.auditPdf(report,signUser);


            String templateWay = jsonConfigService.gettemplateWay(report.getExamItem().getDictValue());

//            if(report.getExamItem().getDictValue().equals("Otol_REM")||report.getExamItem().getDictValue().equals("Otol_PTA")||report.getExamItem().getDictValue().equals("NYST")){
            if ((null != templateWay && (templateWay.equals("UploadPdf")))) {
                numAffected = 1;
                service.auditPdf(report, signUser);
            } else {
                numAffected = service.saveDoc(report);
            }


            //获取最新报告信息，确定是否将报告发送接口
            ExamInfo examInfo = infoService.selectOne(report);
            if (logger.isDebugEnabled()) {
                logger.debug("报告医嘱 {}", examInfo.getOrdIds());
            }
//            if (StringUtils.isNotBlank(examInfo.getOrdId())) {
//                try {
//                    //2.登记医嘱
//                    if((null != examInfo.getInpType() && ExamEnum.InpTypeBodyExam.equals(examInfo.getInpType().getDictValue()))) {
//                        if(logger.isDebugEnabled()) { logger.debug("体检登记{}", examInfo.getExamNo()); }
//                        ordInterService.regOrd(examInfo);
//                    }
//                    //3.发送报告
//                    reportInterService.sendReport(examInfo, ServletUtils.requestContext());
//                    //4.发送结果
//                    infoService.sendReportStatus(examInfo.getId(), ExamEnum.StatusOfSendReportSucess);
//
//                } catch (Exception err) {
//                    infoService.sendReportStatus(examInfo.getId(), ExamEnum.StatusOfSendReportFail);
//                    throw err;
//                }
//            }

            reportInterService.sendReportCCC(examInfo, ResultStatus.REGIST.getValue());
            //infoService.sendReportStatus(examInfo.getId(), ExamEnum.StatusOfSendReportSucess);

            return toAjax(numAffected);
        } catch (Exception err) {
            //退回书写完成工作状态
            try {
                report.getResultStatus().setDictValue(ResultStatus.REPORT.getValue());
                infoService.updateResultStatus(report);
            } catch (Exception er) {
                logger.error("报告状态回退异常,报告id {}，错误信息{}", report.getId(), err);
            }

            logger.error("报告上传异常,报告id {}，错误信息{}", report.getId(), err);
            throw new IllegalAccessException("报告上传异常,错误信息:" + err);
        }
    }

    /**
     * 获取报告审核之后的pdf
     *
     * @param report
     * @return
     * @throws Exception
     */
    @PostMapping("/pdf")
    public ResponseEntity<Resource> openFile(@RequestBody ExamInfo report) throws Exception {
        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=report_audit.pdf");
        Resource resource = new ByteArrayResource(service.getReportPdf(report));
        // 返回ResponseEntity对象
        return ResponseEntity.ok()
                .headers(headers)
                .contentType(MediaType.APPLICATION_PDF)
                .body(resource);
    }

    @GetMapping("/getReportInfo")
    public AjaxResult getReportInfo(HttpServletRequest request) {
        // 请求的地址
        String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        Map ret = new HashMap();
        ret.put("ip", ip);
        String configKey = "reportDesignTemplateConfig";
        String configValue = configService.selectConfigByKey(configKey);
        ret.put("reportDesignTemplateConfig", configValue);
        return AjaxResult.success(ret);
    }

    /**
     * 下载报告
     *
     * @param response
     * @param exam
     */
    @RequestMapping(value = "/file", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    public void readReportDoc(HttpServletResponse response, String exam) {
        if (StringUtils.isBlank(exam)) {
            throw new IllegalArgumentException("缺少报告信息。");
        }
        try {
            if (logger.isDebugEnabled()) {
                logger.debug("下载检查报告< {}", exam);
            }
            //地址栏参数+号变空格
            exam = exam.replace(Const.BLANK, Const.SYMBOL_PLUS);
            //解密
            exam = CryptoUtil.decrypt(exam);
            if (logger.isDebugEnabled()) {
                logger.debug("下载检查报告> {}", exam);
            }
            //id:examNo
            int pos = exam.indexOf(Const.SYMBOL_COLON);
            ExamInfo report = infoService.selectById(Long.valueOf(exam.substring(0, pos)));
            String reportUrl = report.getReportUrlPdf();
            if (StringUtils.isBlank(reportUrl)) {
                throw new RuntimeException("该检查没有生成报告文档。");
            }
            WebUtil.setStreamContent(response, reportUrl.substring(reportUrl.lastIndexOf(Const.SYMBOL_SLASH) + 1));
            reportUrl = yyy.xxx.common.net.storage.utils.WebUtil.putUserinfo(reportUrl, report.getReportUrlUsername(), report.getReportUrlPassword());
            StorageInterface<?, ?> storage = StorageInterfaceFactory.getStorage(reportUrl, Const.STORAGE_SESSION_OPTION);
            try {
                storage.read(reportUrl, response.getOutputStream());
            } catch (Exception err) {
                logger.error(err.getMessage(), err);
                throw err;
            } finally {
                storage.release();
            }
        } catch (Exception err) {
            throw new RuntimeException("读取报告错误。", err);
        }
    }

    /**
     * 下载报告
     *
     * @param mode
     * @param report
     */
    @RequestMapping(value = "/getReport/{mode}/{type}")
    public AjaxResult getReport(@PathVariable Integer mode, @PathVariable String type, @RequestBody ExamInfo report) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            report = infoService.selectById(report.getId());
            String reportUrl = report.getReportUrlPdf();
            if (1 == mode) {
                //获取原始文档
                reportUrl = FileUrlDesUtil.decode(report.getReportUrlOrgPdf());
            } else {
                reportUrl = FileUrlDesUtil.decode(report.getReportUrlPdf());
            }
            if (StringUtils.isBlank(reportUrl) || "" == reportUrl) {
                throw new RuntimeException("该检查没有上传报告文档。");
            }
            reportUrl = yyy.xxx.common.net.storage.utils.WebUtil.putUserinfo(reportUrl, report.getReportUrlUsername(), report.getReportUrlPassword());
            StorageInterface<?, ?> storage = StorageInterfaceFactory.getStorage(reportUrl, Const.STORAGE_SESSION_OPTION);
            try {
                storage.read(reportUrl, outputStream);

                if (type.equals("png")) {
                    Object outData = null;
                    byte[] pdfData = outputStream.toByteArray();
                    BufferedImage[] images = PdfUtil.convJpeg(pdfData);
                    String[] imagesData = new String[images.length];
                    for (int i = 0; i < images.length; i++) {
                        BufferedImage image = images[i];//imageTurnRotate(images[i]) ;
                        byte[] imageData = ImageUtils.getImageData(image);
                        imagesData[i] = yyy.xxx.simpfw.common.utils.StringUtils.byteToBase64(imageData);
                    }
                    outData = imagesData;
                    return AjaxResult.success().put(AjaxResult.DATA_TAG, outData);
                } else {
                    String base64 = yyy.xxx.simpfw.common.utils.StringUtils.byteToBase64(outputStream.toByteArray());
                    InputStream in = new ByteArrayInputStream(base64.getBytes());
                    return AjaxResult.success().put(AjaxResult.DATA_TAG, base64);
                }
            } catch (Exception err) {
                logger.error(err.getMessage(), err);
                throw err;
            } finally {
                storage.release();
            }
        } catch (RuntimeException err) {
            throw err;
        } catch (Exception err) {
            throw new RuntimeException("读取报告错误。", err);
        }
    }

    /**
     * 获取报告图片
     *
     * @param report
     */
    @RequestMapping(value = "/getReportImageData")
    public AjaxResult getReportImageData(@RequestBody ExamInfo report) {

        //图像
        List<ExamAttachment> images = report.getImages();
        if (null == images) {
            service.readReportImages(report);
            images = report.getImages();
        }

        ExamAttachment imgTCDExamData = null;
        List<byte[]> imagesDatas = new ArrayList<>();
        for (int i = 0; null != images && i < images.size(); i++) {
            byte[] imagesData = new byte[0];
            try {
                imagesData = service.readReportImageData(images.get(i));
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
            imagesDatas.add(imagesData);
        }
        return AjaxResult.success().put(AjaxResult.DATA_TAG, imagesDatas);
    }

    @RequestMapping(value = "/withdrawAuditHistReason")
    public AjaxResult withdrawAuditHistReason() {
        return AjaxResult.success(examWithdrawService.withdrawAuditHistReason());
    }

    /**
     * 重新登记、生成、发送报告
     *
     * @param opt        ro-登记，sr-发送报告，ro,sr-登记+发送报告
     * @param id
     * @param createTime
     * @return
     */
    @RequestMapping(value = "/helpReport")
    public AjaxResult helpReport(String opt
            , Long id
            , String examNo
            , Date createTime) {
        try {
            if (StringUtils.isBlank(opt)) {
                return AjaxResult.error("请选择执行的操作");
            }


            if (null == id && null == createTime && StringUtils.isBlank(examNo)) {
                return AjaxResult.error("输入\"检查记录ID\"或者\"检查号\"或者\"登记日期\"。");
            }
            //
            if (!getLoginUser().getUser().isAdmin()) {
                return AjaxResult.error("没有权限。");
            }
            ExamInfoVo params = new ExamInfoVo();
            params.setId(id);
            params.setExamNo(examNo);
            params.setCreateTimeLt(createTime);
            params.setCreateTimeGe(createTime);
            if (null != createTime) {
                params.setResultStatusValues(Arrays.asList(ResultStatus.AUDIT.getValue(), ResultStatus.REAUDIT.getValue()));
            }
            List<ExamInfoVo> rows = infoService.selectList(params);

            reportHelperService.help(ServletUtils.requestContext(), opt, rows);

            return AjaxResult.success();
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }

    @RequestMapping("/uploadOCRReports")
    public AjaxResult uploadOCRReports(@RequestParam("fileInfos") String fileInfos,
                                       @RequestParam(value = "files", required = false) MultipartFile[] files,
                                       @RequestParam(value = "mergeStrategy", defaultValue = "REPLACE") ReportMergeStrategy mergeStrategy) {
        logger.info("merge strategy {}", mergeStrategy);
        StorageInterface<?, ?> storage = null;
        try {
            storage = examReportService.getReportStorage();
            List<FileInfoDto> fileInfoDtos = JSON.parseArray(fileInfos, FileInfoDto.class);
            List<FileInfoDto> allActiveFileInfos = new ArrayList<>();
            Map<String, List<FileInfoDto>> examUidFilesMap = new HashMap<>();
            Map<String, FileInfoDto> filePathMap = new HashMap<>();
            int uploadCount = 0;
            for (FileInfoDto fileInfoDto : fileInfoDtos) {
                ExamInfo examInfo = examInfoService.selectByExamUid(fileInfoDto.getExamUid());
                if (null != examInfo) fileInfoDto.setExamItemCode(examInfo.getExamItem().getDictValue());
//                String matchExamItem = null==examInfo?fileInfoDto.getExamItemCode():examInfo.getExamItem().getDictValue();
                if (StringUtils.isEmpty(fileInfoDto.getDeviceNo()) && StringUtils.isNotEmpty(fileInfoDto.getExamItemCode())) {
                    DicomInfoVo dicomInfo = new DicomInfoVo();
                    dicomInfo.setStatus(0);
                    SysDictData examitem = new SysDictData();
                    examitem.setDictValue(fileInfoDto.getExamItemCode());
                    List<SysDictData> examItems = new ArrayList<>();
                    examItems.add(examitem);
                    dicomInfo.setExamItems(examItems);
                    SysDictData dataSource = new SysDictData();
                    dataSource.setDictCode(fileInfoDto.getDataSourceDictCode());
                    List<DicomInfoVo> dicomInfoVosm = dicomInfoService.selectListJoinExamItem(dicomInfo);
                    if (dicomInfoVosm.isEmpty()) {
                        logger.error("未找到检查项目为{}、数据源为{}对应设备,禁止上传，请联系管理员", fileInfoDto.getExamItemCode(), dataSource.getDictValue());
                        continue;
                    }
                    fileInfoDto.setDeviceNo(dicomInfoVosm.get(0).getDeviceNo());
                }
                switch (fileInfoDto.getFileType()) {
                    case UPLOAD:
                        fileInfoDto.setFile(files[uploadCount++]);
                        break;
                    case HISTORY:
                        if (fileInfoDto.getStatus() == FileStatus.ACTIVE) {//从文件系统获取历史文件
                            String filePath = fileInfoDto.getFilePath();
                            filePath = FileUrlDesUtil.decode(filePath);
                            MultipartFile multipartFile = FileUtil.readFileAsMultipartFile(fileInfoDto.getFileName(),
                                    ArrayUtils.isNotEmpty(files) ? files[0].getContentType() : null,
                                    filePath,
                                    storage);
                            fileInfoDto.setFile(multipartFile);
                            ExamCommonLogEntityWithBLOBs logEntity = examCommonLogService.selectOCRRespByFileId(Long.valueOf(fileInfoDto.getId()));
                            OCRMatchResult ocrMatchResult = OCRMatchResult.builder().build();
                            if (logEntity != null) {
                                ocrMatchResult = JSON.parseObject(logEntity.getRespBody(), OCRMatchResult.class);
                            }
                            fileInfoDto.setOcrMatchResult(ocrMatchResult);
                        }
                        examFileService.updateByPrimaryKeySelective(ExamFileEntity.builder()
                                .id(Long.valueOf(fileInfoDto.getId()))
                                .status(fileInfoDto.getStatusCode()).build());
                        break;
                    case HISTORYNOMATCH:
                        if (fileInfoDto.getStatus() == FileStatus.ACTIVE) {//从文件系统获取历史文件
                            String filePath = fileInfoDto.getFilePath();
                            filePath = FileUrlDesUtil.decode(filePath);
                            if (storage == null) {
                                storage = StorageInterfaceFactory.getStorage(filePath, Const.STORAGE_SESSION_OPTION);
                            }
                            if (storage.fileExists(filePath)) {
                                String newFilePath = FileUtil.doFileNameAddExamNo(filePath, examInfo);
                                if (FileUtil.rename(filePath, newFilePath)) {
                                    filePath = newFilePath;
                                    fileInfoDto.setFilePath(filePath);
                                    fileInfoDto.setFilePath(FileUrlDesUtil.encode(filePath));
                                    fileInfoDto.setFileName(filePath.substring(filePath.lastIndexOf(Const.slash) + 1));
                                }
                            }
                            MultipartFile multipartFile = FileUtil.readFileAsMultipartFile(fileInfoDto.getFileName(),
                                    ArrayUtils.isNotEmpty(files) ? files[0].getContentType() : null,
                                    filePath,
                                    storage);
                            fileInfoDto.setFile(multipartFile);
                            examFileService.updateByPrimaryKeySelective(ExamFileEntity.builder()
                                    .id(Long.valueOf(fileInfoDto.getId()))
                                    .examUid(fileInfoDto.getExamUid())
                                    .examItemCode(fileInfoDto.getExamItemCode())
                                    .fileName(fileInfoDto.getFileName())
                                    .replacedInitFileName(fileInfoDto.getFileName())
                                    .filePath(FileUrlDesUtil.encode(filePath))
                                    .deviceNo(fileInfoDto.getDeviceNo())
                                    .build());
                            //examFileEntity.setId(fileInfoDto.getId());
                            //noMatchEntities.add(examFileEntity);
                        }
                        break;
                    default:
                        throw new IllegalArgumentException(String.format("未知的文件类型 %s", fileInfoDto.getFileType()));
                }
                if (null != fileInfoDto.getExamUid()) {
                    List<FileInfoDto> fileList = examUidFilesMap.computeIfAbsent(fileInfoDto.getExamUid(), k -> new ArrayList<>());
                    fileList.add(fileInfoDto);
                }
                if (fileInfoDto.getStatus() == FileStatus.ACTIVE) {
                    allActiveFileInfos.add(fileInfoDto);
                }
            }

            //存储上传的报告
            StorageInterface<?, ?> finalStorage = storage;
            List<ExamFileEntity> uploadEntities = new ArrayList<>();
            allActiveFileInfos.forEach(fileInfo -> {
                ExamInfo examInfo = examInfoService.selectByExamUid(fileInfo.getExamUid());
                ExamFileEntity examFileEntity = ExamFileEntity.builder()
                        .examUid(fileInfo.getExamUid())
                        .examItemCode(fileInfo.getExamItemCode())
                        .filePath(fileInfo.getFilePath())
                        .fileName(fileInfo.getFileName())
                        .fileMd5(fileInfo.getFileMd5())
                        .fileSize(fileInfo.getFileSize())
                        .uploadTime(fileInfo.getUploadTime())
                        .ocrFileLabel(fileInfo.getFileLabel())
                        .status(fileInfo.getStatusCode())
                        .deviceNo(fileInfo.getDeviceNo())
                        .dataSourceDictCode(fileInfo.getDataSourceDictCode())
                        .build();
                if (fileInfo.getFileType() == FileType.UPLOAD) {
                    SysDept sysDept = null;
                    if (null != fileInfo.getDeviceNo()) {
                        DicomInfo dicomInfo = new DicomInfo();
                        dicomInfo.setDeviceNo(fileInfo.getDeviceNo());
                        DicomInfoVo dicomInfoVo = dicomInfoService.selectOne(dicomInfo);
                        if (null != dicomInfoVo) {
                            sysDept = dicomInfoVo.getDept();
                        }
                    }

                    if (null == sysDept&&null!= fileInfo.getDataSourceDictCode()) {
                        DicomInfoVo dicomInfo = new DicomInfoVo();
                        dicomInfo.setStatus(0);
                        if(null != fileInfo.getExamItemCode()){
                            SysDictData examitem = new SysDictData();
                            examitem.setDictValue(fileInfo.getExamItemCode());
                            List<SysDictData> examItems = new ArrayList<>();
                            examItems.add(examitem);
                            dicomInfo.setExamItems(examItems);
                        }
                        SysDictData dataSource = new SysDictData();
                        dataSource.setDictCode(fileInfo.getDataSourceDictCode());
                        List<DicomInfoVo> dicomInfoVosm = dicomInfoService.selectListJoinExamItem(dicomInfo);
                        if (!dicomInfoVosm.isEmpty()) {
                            sysDept = dicomInfoVosm.get(0).getDept();
                        }
                    }

                    if (null == sysDept) {
                        LoginUser loginUser = getLoginUser();
                        sysDept = loginUser.getUser().getDept();
                    }

                    if (null == sysDept) {
                        logger.error("文件:{}上传失败,无法找到对应科室", fileInfo.getFileName());
                        return;
                    }

                    String uploadFilePath = FileUtil.getReportUploadFile2Url(sysDept, examInfo, fileInfo);
                    try {
                        URI uploadFileFullPath = FileUtil.storageFile(fileInfo.getFile(), finalStorage, uploadFilePath);
                        String fileUrl = URLDecoder.decode(uploadFileFullPath.toString(), Const.charset_UTF_8);
                        fileInfo.setFilePath(FileUrlDesUtil.encode(fileUrl));
                        examFileEntity.setFilePath(fileInfo.getFilePath());
                        uploadEntities.add(examFileEntity);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
                filePathMap.put(fileInfo.getFilePath(), fileInfo);
            });
            examFileService.batchInsert(uploadEntities);
            int batchInsertCount = uploadEntities.size();
            Long currentUserId = SecurityUtils.getUserId();
            for (ExamFileEntity entity : uploadEntities) {
                CompletableFuture.runAsync(() -> {
                    ExamInfo examInfo = examInfoService.selectByExamUid(entity.getExamUid());
                    ExamCommonLogEntityWithBLOBs logEntity = new ExamCommonLogEntityWithBLOBs();
                    logEntity.setFileId(entity.getId());
                    logEntity.setEventType(CommonEventType.OCR.name());
                    if (null != examInfo) logEntity.setExamId(examInfo.getId());
                    logEntity.setRespBody(JSON.toJSONString(filePathMap.get(entity.getFilePath()).getOcrMatchResult()));
                    logEntity.setCreateUser(currentUserId);
                    examCommonLogService.insertSelective(logEntity);
                });
            }

            Map<String, List<ExamFileEntity>> examUidFileEntitiesMap = new HashMap<>();
            for (String examUid : examUidFilesMap.keySet()) {
                if (CollectionUtils.isEmpty(examUidFilesMap.get(examUid))) {
                    ExamInfo examInfo = infoService.selectByExamUid(examUid);
                    examInfo.setReportUrlOrgPdf(null);
                    examInfo.setReportUploadFilename(null);
                    infoService.update(examInfo);
                    continue;
                }
                ExamFileEntityExample example = new ExamFileEntityExample();
                example.createCriteria().andExamUidEqualTo(examUid).andStatusEqualTo(0);
                List<ExamFileEntity> fileEntities = examFileService.selectByExample(example);
                if (CollectionUtils.isEmpty(fileEntities)) {
                    continue;
                }
                examUidFileEntitiesMap.put(examUid, fileEntities);
                if (mergeStrategy == ReportMergeStrategy.REPLACE) {
                    ExamInfo examInfo = infoService.selectByExamUid(examUid);
                    if (ReportMergeStrategy.REPLACE == jsonConfigService.getReportMergeStrategy(examInfo.getExamItem().getDictValue())) {
                        //开始替换fileLabel相同的文件
                        Map<String, ExamFileEntity> labelFileMap = new HashMap<>();
                        ExamFileEntityExample examFileEntityExample = new ExamFileEntityExample();
                        examFileEntityExample.createCriteria().toString();

                        for (ExamFileEntity fileEntity : fileEntities) {
                            if (StringUtils.isNotBlank(fileEntity.getOcrFileLabel())) {
                                labelFileMap.computeIfPresent(fileEntity.getOcrFileLabel(), (k, v) -> {
                                    v.setStatus(FileStatus.DELETED.getCode());
                                    examFileService.updateByPrimaryKeySelective(v);
                                    fileEntity.setReplacedFileId(v.getId());
                                    fileEntity.setReplacedInitFileName(v.getReplacedInitFileName());
                                    examFileService.updateByPrimaryKeySelective(fileEntity);
                                    return fileEntity;
                                });
                                labelFileMap.put(fileEntity.getOcrFileLabel(), fileEntity);
                            }
                        }
                    }
                }
            }

            //合并pdf并上传
            Map<String, List<FunctionDefinition>> functionMap = ocrService.getAllFunctionMapping();
            examUidFileEntitiesMap.forEach((examUid, fileList) -> {
                try {
                    fileList = fileList.stream()
                            .filter(examFileEntity -> examFileEntity.getStatus() == FileStatus.ACTIVE.getCode())
                            .collect(Collectors.toList());
                    fileList.sort(Comparator.comparing(ExamFileEntity::getReplacedInitFileName));
                    ExamInfo examInfo = infoService.selectByExamUid(examUid);
                    JSONObject fileNameJs = new JSONObject();

                    for (int i = 0; i < fileList.size(); i++) {
                        ExamFileEntity fileEntity = fileList.get(i);
                        fileNameJs.put(fileEntity.getFileName(), i);

                        if (StringUtils.isNotBlank(fileEntity.getOcrFileLabel())) {
                            if (functionMap.get(fileEntity.getOcrFileLabel())
                                    .stream().anyMatch(function -> function.getFunctionType() == FunctionType.DIAG_AREA)) {
                                fileEntity.setOcrDiagTaskStatus(OCRTaskStatus.PENDING);
                                examFileService.updateByPrimaryKeySelective(fileEntity);
                            }
                        }
                    }
                    examInfo.setReportUploadFilename(fileNameJs.toString());

                    MultipartFile mergeFile = null;
                    if (CollectionUtils.isNotEmpty(fileList)) {
                        if (fileList.size() == 1) {
                            mergeFile = filePathMap.get(fileList.get(0).getFilePath()).getFile();
                        } else {
                            mergeFile = service.manipulatePdf(fileList.stream()
                                    .map(examFileEntity -> filePathMap.get(examFileEntity.getFilePath()).getFile())
                                    .collect(Collectors.toList()));
                        }
                    }
                    if (mergeFile != null) {
                        service.uploadReport(examInfo, null, mergeFile, getLoginUser().getUser());
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
            AjaxResult response = AjaxResult.success(String.format("成功新增%d个文件记录。", batchInsertCount));
            response.put("batchInsertCount", batchInsertCount);
            return response;
        } catch (Exception err) {
            String errM = err.getMessage();
            logger.error(errM, err);
            return AjaxResult.error(errM);
        } finally {
            if (storage != null) {
                storage.release();
            }
        }
    }


    /**
     * pdf报告批量上传
     *
     * @param ExamInfoArs 目标的检查/报告
     * @param files       报告文件
     */
    @RequestMapping("/uploadReports")
    public AjaxResult uploadReports(@RequestParam("ExamInfos") String ExamInfoArs,
                                    @RequestParam("files") MultipartFile[] files) {
        try {
            AjaxResult r = null;

            List<ExamInfo> ExamInfos = JSON.parseArray(ExamInfoArs, ExamInfo.class);

            Map<String, MultipartFile> fileNameMapFile = new HashMap<>();
            for (MultipartFile file : files) {
                String fileName = file.getOriginalFilename();
                fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
                fileNameMapFile.put(fileName, file);
            }

            for (ExamInfo examInfo : ExamInfos) {
                ExamInfo examInfoU = infoService.selectOne(examInfo);
                JSONObject fileNameJs = new JSONObject();

                String fileNames = examInfoU.getReportUploadFilename();
                if (null != fileNames && yyy.xxx.simpfw.common.utils.StringUtils.isNotBlank(fileNames)) {
                    fileNameJs = JSON.parseObject(fileNames);
                }
                for (int i = 0; i < examInfo.getFileNames().size(); i++) {
                    fileNameJs.put(examInfo.getFileNames().get(i), i);
                }
                examInfo.setReportUploadFilename(fileNameJs.toString());

                //合并pdf文件
                if (examInfo.getFileNames().size() > 1) {
                    List<MultipartFile> fileList = new ArrayList<>();
                    for (String fileName : examInfo.getFileNames()) {
                        fileList.add(fileNameMapFile.get(fileName));
                    }
                    MultipartFile file = service.manipulatePdf(fileList);
                    examInfo.getFileNames().clear();
                    examInfo.getFileNames().add(file.getOriginalFilename());
                    fileNameMapFile.put(file.getOriginalFilename(), file);
                }

                examInfo.setReportUrlOrgPdf(FileUrlDesUtil.encode(examInfoU.getReportUrlOrgPdf()));
                if (null != examInfo.getReportUrlOrgPdf()) {
                    MultipartFile file2 = fileNameMapFile.get(examInfo.getFileNames().get(0));
                    MultipartFile file = service.manipulateExamPdf(file2, examInfo);
                    fileNameMapFile.put(file.getOriginalFilename(), file);
                }

                if (examInfo.getFileNames().size() > 0) {
                    MultipartFile file = fileNameMapFile.get(examInfo.getFileNames().get(0));
                    int num = service.uploadReportV1(examInfo, null, file, getLoginUser().getUser());

                    r = AjaxResult.success(String.format("导入%d个文件。", num));
                    r.put("numAffected", num);
                }
            }
            return r;
        } catch (Exception err) {
            String errM = err.getMessage();
            logger.error(errM, err);
            return AjaxResult.error(errM);
        }
    }


    @Log(title = "报告回退", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasAnyPermi('exam-report:write')")
    @PostMapping(value = "/resultStatusRollback")
    public AjaxResult resultStatusRollback(@RequestBody ExamInfo entity) {
        ExamInfo info = infoService.selectOne(entity);
        if (null == info) {
            return AjaxResult.error("检查不存在。");
        }
        return toAjax(service.resultStatusRollback(info));
    }

    @Log(title = "报告数据清空", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasAnyPermi('exam-report:write')")
    @PostMapping(value = "/reportDataClear")
    public AjaxResult reportDataClear(@RequestBody ExamInfo entity) {
        ExamInfo info = infoService.selectOne(entity);
        if (null == info) {
            return AjaxResult.error("检查不存在。");
        }
        return toAjax(service.reportDataClear(info));
    }

    private List<ExamInfoVo> getPatient(ExamInfoVo examInfo,
                                        PatientVo patientInfo,
                                        List<String> examItems) {
        List<String> resultStatusValues = new ArrayList<>();
        resultStatusValues.add(ResultStatus.REGIST.getValue());
        resultStatusValues.add(ResultStatus.EXAM.getValue());
        resultStatusValues.add(ResultStatus.REPORT.getValue());
        if (examInfo == null) {
            examInfo = new ExamInfoVo();
        }
        examInfo.setExamItemCodes(examItems);
        examInfo.setPatientInfo(patientInfo);
        examInfo.setResultStatusValues(resultStatusValues);
        examInfo.setStatus(0);
        return infoService.selectList(examInfo);
    }

    private OCRRecResult findValuesByMatchRule(OCRMatchRule finalOcrMatchRule,
                                               List<OCRTextInfo> ocrTextInfos,
                                               Map<String, String> fileLabelExamItemMap){
        OCRRecResult ocrRecResult = ocrService.findValuesByMatchRule(finalOcrMatchRule, ocrTextInfos);
        ocrRecResult.setOcrTextInfos(ocrTextInfos);

        if (StringUtils.isNotBlank(ocrRecResult.getFileLabel())) {
            String matchExamItem = fileLabelExamItemMap.get(ocrRecResult.getFileLabel());
            ocrRecResult.setExamItem(matchExamItem);
        }
        return ocrRecResult;
    }

    private OCRMatchRule getOCRMatchRule(List<String> examItems,Map<String, String> fileLabelExamItemMap){
        Map<String, JSONObject> ocrMatchRules = ocrService.getExamMatchRules();
        OCRMatchRule finalOcrMatchRule = new OCRMatchRule();
        MatchArea matchArea = null;
        for (String examItem : examItems) {
            if (ocrMatchRules.get(examItem) != null) {
                OCRMatchRule ocrMatchRule = ocrMatchRules.get(examItem).toJavaObject(OCRMatchRule.class);
                finalOcrMatchRule.getExamItems().add(examItem);
                finalOcrMatchRule.getFindFunction().putAll(ocrMatchRule.getFindFunction());

                for (String fileLabel : ocrMatchRule.getFindFunction().keySet()) {
                    fileLabelExamItemMap.put(fileLabel, examItem);
                }

                //取并集
                if (matchArea == null) {
                    matchArea = ocrMatchRule.getMatchArea();
                    continue;
                }
                int startX = Math.min(matchArea.getStartX(), ocrMatchRule.getMatchArea().getStartX());
                matchArea.setStartX(startX);
                int startY = Math.min(matchArea.getStartY(), ocrMatchRule.getMatchArea().getStartY());
                matchArea.setStartY(startY);
                int cutHeight = (matchArea.getCutHeight() != -1 && ocrMatchRule.getMatchArea().getCutHeight() != -1)
                        ? Math.max(matchArea.getCutHeight(), ocrMatchRule.getMatchArea().getCutHeight())
                        : -1;
                matchArea.setCutHeight(cutHeight);
                int cutWidth = (matchArea.getCutWidth() != -1 && ocrMatchRule.getMatchArea().getCutWidth() != -1)
                        ? Math.max(matchArea.getCutWidth(), ocrMatchRule.getMatchArea().getCutWidth())
                        : -1;
                matchArea.setCutWidth(cutWidth);
            }
        }
        if (matchArea == null) {
            throw new RuntimeException(String.format("至少在检查项目: %s 中配置一个ocr匹配规则", examItems));
        }
        finalOcrMatchRule.setMatchArea(matchArea);
        return finalOcrMatchRule;
    }

    private OCRRecResult doOCRRecognition(byte[] file, List<String> examItems) {
        //检查必要的配置
        String ocrServerURL = ocrService.getOCRServerUrl();
        examItems = Optional.of(examItems)
                .orElseThrow(() -> new IllegalArgumentException("examItems is null"));
        if (examItems.isEmpty()) {
            throw new IllegalArgumentException("examItems is empty");
        }
        Map<String, String> fileLabelExamItemMap = new HashMap<>(); //通过某个种类的报告文件查询对应的检查项目，更精确匹配检查记录
        OCRMatchRule finalOcrMatchRule = getOCRMatchRule(examItems,fileLabelExamItemMap);

        logger.info("成功获取ocr配置: {}", finalOcrMatchRule);
        long startTimeTotal = System.currentTimeMillis();
        //请求ocr服务
        List<OCRTextInfo> ocrTextInfos = ocrService.pdfOCR(ocrServerURL, file, finalOcrMatchRule.getMatchArea());
        //根据MatchRule匹配对应业务字段
        OCRRecResult ocrRecResult = ocrService.findValuesByMatchRule(ocrServerURL, file, finalOcrMatchRule, ocrTextInfos);
        ocrRecResult.setOcrTextInfos(ocrTextInfos);

        if (StringUtils.isNotBlank(ocrRecResult.getFileLabel())) {
            String matchExamItem = fileLabelExamItemMap.get(ocrRecResult.getFileLabel());
            ocrRecResult.setExamItem(matchExamItem);
        }
        long endTimeTotal = System.currentTimeMillis();
        logger.info("ocr recognition total time: {}", (endTimeTotal - startTimeTotal));
        return ocrRecResult;
    }

    private OCRMatchResult doOCRMatch(OCRRecResult ocrRecResult) {
        // 查找匹配的报告
        ExamInfoVo examInfoVo = null;
        PatientVo patientVo = null;
        for (Object foundValue : ocrRecResult.getFoundValues()) {
            if (foundValue instanceof ExamInfoVo) {
                examInfoVo = (ExamInfoVo) foundValue;
                continue;
            }
            if (foundValue instanceof PatientVo) {
                patientVo = (PatientVo) foundValue;
            }
        }
        List<ExamInfoVo> listExam = new ArrayList<>();
        if (!ocrRecResult.getFoundValues().isEmpty()) {
            List<String> examItems = new ArrayList<>();
            examItems.add(ocrRecResult.getExamItem());
            listExam = getPatient(examInfoVo, patientVo, examItems);
        }

        return OCRMatchResult.builder()
                .ocrContents(ocrRecResult.getOcrTextContents())
                .examInfos(listExam)
                .foundValueMsg(ocrRecResult.getFoundValueMsg())
                .fileLabel(ocrRecResult.getFileLabel())
                .examItemCode(ocrRecResult.getExamItem())
                .build();
    }

    private OCRMatchResult doOCRRecAndMatch(byte[] file, List<String> examItems) {
        OCRRecResult ocrRecResult = doOCRRecognition(file, examItems);
        return doOCRMatch(ocrRecResult);
    }

    @RequestMapping("/searchFileExist")
    public AjaxResult searchFileExist(@RequestParam("examItems") List<String> examItems,
                                      @RequestParam String fileInfo) {
        AjaxResult ajaxResult = AjaxResult.success();
        try {
            int count = examFileService.searchFileExist(JSON.parseObject(fileInfo, FileInfoDto.class), examItems, new ArrayList<>());
            logger.info("找到 {} 条MD5相同且文件名相同的记录", count);
            ajaxResult.put(AjaxResult.DATA_TAG, count);
        } catch (Exception err) {
            String errM = err.getMessage();
            logger.error(errM, err);
            return AjaxResult.error(errM);
        }
        return ajaxResult;
    }

    @RequestMapping("/getFileInfosByExamUid")
    public AjaxResult getFileInfosByExamUid(@RequestParam("examUid") String examUid) {
        AjaxResult ajaxResult = AjaxResult.success();
        try {
            List<FileInfoDto> fileInfoDtos = examFileService.getExamFilesByExamUid(examUid);
            ajaxResult.put(AjaxResult.DATA_TAG, fileInfoDtos);
        } catch (Exception err) {
            String errM = err.getMessage();
            logger.error(errM, err);
            return AjaxResult.error(errM);
        }
        return ajaxResult;
    }

    @RequestMapping("/deleteFilesByIds")
    public AjaxResult deleteFilesByIds(@RequestParam("ids") List<Long> ids) {
        AjaxResult ajaxResult = AjaxResult.success();
        try {
            for (Long id : ids) {
                examFileService.updateStatusByID(id, FileStatus.DELETED);
            }
        } catch (Exception err) {
            String errM = err.getMessage();
            logger.error(errM, err);
            return AjaxResult.error(errM);
        }
        return ajaxResult;
    }

//    @RequestMapping("/getFileInfos")
//    public AjaxResult getFileInfos(@RequestParam String fileInfo) {
//        AjaxResult ajaxResult = AjaxResult.success();
//        try {
//            FileInfoDto fileInfoDto = JSON.parseObject(fileInfo, FileInfoDto.class);
//            List<FileInfoDto> fileInfoDtos = examFileService.getFileInfos(fileInfoDto);
//            ajaxResult.put(AjaxResult.DATA_TAG, fileInfoDtos);
//        } catch (Exception err) {
//            String errM = err.getMessage();
//            logger.error(errM, err);
//            return AjaxResult.error(errM);
//        }
//        return ajaxResult;
//    }

    @RequestMapping("/getUnmatchFileInfos")
    public TableDataInfo getUnmatchFileInfos(@RequestParam("examItemCodes") List<String> examItemCodes,
                                             @RequestParam(value = "createTimeGe", required = false) Date createTimeGe,
                                             @RequestParam(value = "createTimeLt", required = false) Date createTimeLt) {
        AjaxResult ajaxResult = AjaxResult.success();
        LoginUser luser = getLoginUser();
        List<Long> dataSourceDictCodes = new ArrayList<>();
        if (luser != null && null != luser.getDeptId()) {
            List<DicomInfoVo> dicomInfos = new ArrayList<>();
            SysDept sysDept = new SysDept();
            sysDept.setDeptId(luser.getDeptId());
            DicomInfo dicomInfo = new DicomInfo();
            dicomInfo.setStatus(0);
            dicomInfo.setDept(sysDept);
            dicomInfos = dicomInfoService.selectList(dicomInfo);
            for (DicomInfo dicomInfo1 : dicomInfos) {
                if (null != dicomInfo1.getDataSource())
                    dataSourceDictCodes.add(dicomInfo1.getDataSource().getDictCode());
            }
        }
        startPage();
        List<FileInfoDto> fileInfoDtos = new ArrayList<>();
        try {
            fileInfoDtos = examFileService.getUnmatchFileInfos(examItemCodes, dataSourceDictCodes, createTimeGe, createTimeLt);
        } catch (Exception err) {
            String errM = err.getMessage();
            logger.error(errM, err);
        }
        return getDataTable(fileInfoDtos);
    }

    /**
     * 登记匹配错误报告
     */
    @Log(title = "登记匹配错误报告", businessType = BusinessType.GRANT)
    @PutMapping(value = "/examMatchFile")
    public AjaxResult examMatchFile(@RequestBody ExamInfo param) {
        //
        List<String> examItemCodes = new ArrayList<>();
        examItemCodes.add(param.getExamItem().getDictValue());

        LoginUser luser = getLoginUser();
        List<Long> dataSourceDictCodes = new ArrayList<>();
        if (luser != null && null != luser.getDeptId()) {
            List<DicomInfoVo> dicomInfos = new ArrayList<>();
            SysDept sysDept = new SysDept();
            sysDept.setDeptId(luser.getDeptId());
            DicomInfo dicomInfo = new DicomInfo();
            dicomInfo.setStatus(0);
            dicomInfo.setDept(sysDept);
            dicomInfos = dicomInfoService.selectList(dicomInfo);
            for (DicomInfo dicomInfo1 : dicomInfos) {
                if (null != dicomInfo1.getDataSource())
                    dataSourceDictCodes.add(dicomInfo1.getDataSource().getDictCode());
            }
        }

        List<FileInfoDto> fileInfoDtos = examFileService.getUnmatchFileInfos(examItemCodes, dataSourceDictCodes, null, null);
        for(FileInfoDto fileInfoDto : fileInfoDtos) {
            OCRMatchResult ocrMatchResult =  fileInfoDto.getOcrMatchResult();
            if(null== ocrMatchResult || CollectionUtils.isEmpty(ocrMatchResult.getOcrTextInfos())) {
                continue; //没有匹配结果的文件不处理
            }

            Map<String, String> fileLabelExamItemMap = new HashMap<>(); //通过某个种类的报告文件查询对应的检查项目，更精确匹配检查记录
            OCRMatchRule finalOcrMatchRule = getOCRMatchRule(examItemCodes,fileLabelExamItemMap);

            OCRRecResult ocrRecResult1 = findValuesByMatchRule(finalOcrMatchRule, ocrMatchResult.getOcrTextInfos(), fileLabelExamItemMap);

            OCRMatchResult ocrMatchResultM =  doOCRMatch(ocrRecResult1);
            if(null!=ocrMatchResultM.getExamInfos()&&1==ocrMatchResultM.getExamInfos().size()) {
                ExamInfoVo examInfo = ocrMatchResultM.getExamInfos().get(0);
                if(!param.getExamUid().equals(examInfo.getExamUid())) return AjaxResult.success();
                fileInfoDto.setOcrMatchResult(ocrMatchResultM);
                fileInfoDto.setFileType(FileType.HISTORYNOMATCH);

                fileInfoDto.setExamUid(examInfo.getExamUid());
                fileInfoDto.setExamItemCode(examInfo.getExamDoctorsCode());

                List<FileInfoDto> fileInfoDtosI = new ArrayList<>();
                fileInfoDtosI.add(fileInfoDto);
                this.uploadOCRReports(JSON.toJSONString(fileInfoDtosI), null,ReportMergeStrategy.APPEND);
            }
        }
        return AjaxResult.success();
    }

    @Log(title = "报告自动上传", businessType = BusinessType.INSERT)
    @PostMapping("/autoOCRMatch")
    public AjaxResult autoOCRMatch(@RequestParam("examItemCodes") List<String> examItemCodes,
                                   @RequestParam("files") MultipartFile[] files,
                                   @RequestParam(value = "dataSourceDictValue", required = false) String dataSourceDictValue) {
        //dataSourceDictValue = "test";
        if (StringUtils.isEmpty(dataSourceDictValue)) {
            return AjaxResult.error("数据源不能为空!");
        }
        // 创建数据源字典数据对象并设置字典值
        SysDictData dataSource = new SysDictData();
        dataSource.setDictValue(dataSourceDictValue);
        dataSource.setDictType(Const.DATA_SOURCE_DICT_TYPE);

        SysDictData queryDataSource = dictDataService.selectDictDataByValue(dataSource);
        if (null == queryDataSource) {
            return AjaxResult.error("数据源:{}不存在!", dataSourceDictValue);
        }

        // 创建DicomInfoVo对象并设置数据源和状态
        DicomInfoVo queryDicomInfo = new DicomInfoVo();
        queryDicomInfo.setDataSource(dataSource);
        queryDicomInfo.setStatus(0);

        // 调用服务查询DicomInfoVo列表
        List<DicomInfoVo> dicomInfoVos = dicomInfoService.selectList(queryDicomInfo);
        // 如果查询结果为空，返回错误信息
        if (dicomInfoVos.isEmpty()) {
            return AjaxResult.error("数据源：" + dataSourceDictValue + "未配置设备，请联系管理员");
        }
        // 获取数据源下的检查项目
        List<String> dataSourceExamItemCodes = dicomInfoVos.stream()
                .flatMap(dicomInfoVo -> dicomInfoVo.getExamItems().stream())
                .map(SysDictData::getDictValue)
                .collect(Collectors.toList());
        if (dataSourceExamItemCodes.isEmpty()) {
            // 获取当前用户可查看的检查项目
            Map<String, List<ExamDataCtrlDetail>> edatas = dataCtrlService.findDataScope(getLoginUser().getUser());
            List<ExamDataCtrlDetail> dicts = edatas.get("dict");
            if (CollectionUtils.isNotEmpty(dicts)) {
                dicts.forEach(dict -> {
                    if (StringUtils.startsWith(dict.getItemId(), "uis_exam_item:")) {
                        dataSourceExamItemCodes.add(StringUtils.substringAfter(dict.getItemId(), "uis_exam_item:"));
                    }
                });
            }
        }
        try {
            Map<String, FileInfoDto> fileNameInfoMap = new HashMap<>();
            files = Arrays.stream(files).filter(file -> {
                try {
                    String fileMd5 = DigestUtils.md5Hex(file.getInputStream());
                    FileInfoDto fileInfoDto = FileInfoDto.builder()
                            .fileName(FileUtils.getName(file.getOriginalFilename()))
                            .fileMd5(fileMd5)
                            .fileType(FileType.UPLOAD)
                            .fileSize(file.getSize())
                            .file(file)
                            .uploadTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai")))
                            .status(FileStatus.ACTIVE)
                            .dataSourceDictCode(dicomInfoVos.get(0).getDataSource().getDictCode())
                            .build();
                    fileNameInfoMap.put(file.getOriginalFilename(), fileInfoDto);
                    int count = this.examFileService
                            .searchFileExist(fileInfoDto, dataSourceExamItemCodes, Collections.singletonList(queryDataSource.getDictCode()));
                    fileInfoDto.setFileName(file.getOriginalFilename());
                    if (count > 0) {
                        logger.debug("文件 {} 已存在, 跳过,上传时间{}", file.getOriginalFilename(), fileInfoDto.getUploadTime());
                    }
                    return count == 0;
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }).toArray(MultipartFile[]::new);

            if (files.length == 0) {
                return AjaxResult.success("跳过了所有上传的文件");
            }

            AjaxResult ocrMatchResult = this.ocrMatch(dataSourceExamItemCodes, files);
            if (!ocrMatchResult.isSuccess()) {
                return ocrMatchResult;
            }
            Map<String, OCRMatchResult> fileOCRResultMap = (Map<String, OCRMatchResult>) ocrMatchResult.get(AjaxResult.DATA_TAG);
            if (fileOCRResultMap.isEmpty()) {
                return AjaxResult.success("没有匹配到任何检查记录");
            }

            List<MultipartFile> fileList = new ArrayList<>();
            List<FileInfoDto> fileInfos = new ArrayList<>();
            fileOCRResultMap.forEach((key, value) -> {
                List<FileInfoDto> fileInfoDtoList;
                FileInfoDto fileInfoDto = fileNameInfoMap.get(key);
                fileInfoDto.setOcrMatchResult(value);
                if (value.getExamInfos().size() == 1) {
                    ExamInfoVo examInfo = value.getExamInfos().get(0);
                    fileInfoDto.setExamUid(examInfo.getExamUid());
                    fileInfoDto.setExamItemCode(examInfo.getExamDoctorsCode());
                    // 获取检查对应的历史文件
                    fileInfoDtoList = examInfo.getFileInfos();
                } else {
                    // 对 size 不等于 1 的情况应用另一种处理逻辑
                    fileInfoDtoList = new ArrayList<>();
                    fileInfoDto.setExamItemCode(value.getExamItemCode());
                }

                fileInfoDtoList.add(fileInfoDto);
                fileList.add(fileInfoDto.getFile());
                fileInfos.addAll(fileInfoDtoList);
            });

            // 如果需要对 fileInfos 进行进一步处理，可以在这里继续操作
            if (CollectionUtils.isNotEmpty(fileList)) {
                AjaxResult res = this.uploadOCRReports(JSON.toJSONString(fileInfos),
                        fileList.toArray(new MultipartFile[0]),
                        ReportMergeStrategy.REPLACE);
                if (0 == (int) res.get("batchInsertCount")) return AjaxResult.error("上传文件失败");
                return res;
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return AjaxResult.error(e.getMessage());
        }

        return AjaxResult.success();
    }

    @RequestMapping("/ocrMatch")
    public AjaxResult ocrMatch(@RequestParam("examItems") List<String> examItems, @RequestParam("files") MultipartFile[] files) {
        AjaxResult res;
        Map<String, OCRMatchResult> fileOCRResultMap = new HashMap<>();
        for (MultipartFile file : files) {
            logger.info("ocr识别文件名: {}, 检查项目: {}", file.getOriginalFilename(), examItems);
            try {
                OCRMatchResult matchResult = doOCRRecAndMatch(file.getBytes(), examItems);
                fileOCRResultMap.put(file.getOriginalFilename(), matchResult);
                List<ExamInfoVo> listExam = matchResult.getExamInfos();
                if (CollectionUtils.isNotEmpty(listExam) && listExam.size() == 1) {
                    List<FileInfoDto> fileInfoDtos = examFileService.getExamFilesByExamUid(listExam.get(0).getExamUid());
                    listExam.get(0).setFileInfos(fileInfoDtos);
                }
            } catch (Exception err) {
                String errM = err.getMessage();
                logger.error(errM, err);
                return AjaxResult.error(errM);
            }
        }

        res = AjaxResult.success();
        res.put(AjaxResult.DATA_TAG, fileOCRResultMap);
        return res;
    }

    @RequestMapping("/ocrMatchTest")
    public AjaxResult ocrMatchTest(@RequestParam(value = "limit", defaultValue = "-1") int limit) throws Exception {
        ExamFileEntityExample example = new ExamFileEntityExample();
        example.createCriteria().andStatusEqualTo(0);
        if (limit >= 0) {
            PageHelper.startPage(1, limit);
        }
        List<ExamFileEntity> examFileEntities = examFileService.selectByExample(example);
        StorageInterface<?, ?> storage = examReportService.getReportStorage();
        for (ExamFileEntity examFileEntity : examFileEntities) {
            String filePath = examFileEntity.getFilePath();
            byte[] file = FileUtil.readFileAsByteArray(filePath, storage);
            doOCRRecognition(file, Collections.singletonList(examFileEntity.getExamItemCode()));
        }
        return AjaxResult.success();
    }

    @RequestMapping("/getFile")
    public AjaxResult getFile(@RequestParam("filePath") @NotBlank(message = "文件路径不能为空") String filePath) {
        AjaxResult ajaxResult = AjaxResult.success();
        try {
            String fileBase64 = FileUtil.readFileAsBase64(FileUrlDesUtil.decode(filePath));
            ajaxResult.put(AjaxResult.DATA_TAG, fileBase64);
        } catch (Exception err) {
            String errM = err.getMessage();
            logger.error(errM, err);
            return AjaxResult.error(errM);
        }

        return ajaxResult;
    }

    @RequestMapping("/getVirtualPrinterFile")
    public AjaxResult getVirtualPrinterFile(@RequestParam("subPath") @NotBlank(message = "文件路径不能为空") String subPath) {
        AjaxResult ajaxResult = AjaxResult.success();
        logger.info("前端请求获取虚拟打印机文件 {}", subPath);
        try {
            StorageParam storageConfig = examReportService.getStorageParam(ExamReportService.virtualPrinterStorageConfigKey);
            String filePath = storageConfig.getLoc() + (storageConfig.getLoc().contains("/") ? "/" : "\\") + subPath;
            logger.info("文件完整路径：{}", filePath);

            String fileBase64 = FileUtil.readFileAsBase64(filePath);
            ajaxResult.put(AjaxResult.DATA_TAG, fileBase64);
        } catch (Exception err) {
            String errM = err.getMessage();
            logger.error(errM, err);
            return AjaxResult.error(errM);
        }

        return ajaxResult;
    }

    /**
     * 浏览报告
     *
     * @param report 报告信息
     */
    @RequestMapping(value = "/view/{mode}")
    public AjaxResult view(@PathVariable Integer mode, @RequestBody ExamInfo report) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();) {

//            if (report.getExamItem().getDictValue().equals("TCD-I")) {
//                selectImage(report);
//            }
            if (ResultStatus.AUDIT.is(report.getResultStatus().getDictValue())) {
                if (null == mode || 0 == mode) {
                    return getReport(0, "pdf", report);
                } else if (1 == mode) {
                    return getReport(0, "png", report);
                }
            }


            String templateWay = jsonConfigService.gettemplateWay(report.getExamItem().getDictValue());

//            if(report.getExamItem().getDictValue().equals("Otol_REM")||report.getExamItem().getDictValue().equals("Otol_PTA")||report.getExamItem().getDictValue().equals("NYST")){
            if (null != templateWay && (templateWay.equals("OnlyStructTemplate") || templateWay.equals("EndOfStructTemplate"))) {
                service.createDoc2(outputStream, report, true, templateWay);
            } else if ((null != templateWay && (templateWay.equals("UploadPdf")))) {
                return getReport(1, "pdf", report);
            } else {
                service.createDoc(outputStream, report, true, templateWay);
            }

            byte[] pdfData = outputStream.toByteArray();

            //
            Object outData = null;
            //请求的数据类型
            if (null == mode || 0 == mode) {
                outData = yyy.xxx.simpfw.common.utils.StringUtils.byteToBase64(pdfData);
            } else if (1 == mode) {
                int lent = pdfData.length;
                BufferedImage[] images = PdfUtil.convJpeg(pdfData);
                String[] imagesData = new String[images.length];
                for (int i = 0; i < images.length; i++) {
                    BufferedImage image = images[i];
                    byte[] imageData = ImageUtils.getImageData(image);
                    imagesData[i] = yyy.xxx.simpfw.common.utils.StringUtils.byteToBase64(imageData);
                }
                outData = imagesData;
            }
            return AjaxResult.success().put(AjaxResult.DATA_TAG, outData);
        } catch (Exception err) {
            logger.error(err.getMessage(), err);
            return AjaxResult.error(err.getMessage());
        }
    }
}
